package price

import (
	"context"
	"errors"
	"fmt"
	"sort"

	"github.com/buger/jsonparser"
	"github.com/goccy/go-json"
	"github.com/spf13/cast"
	"hexcloud.cn/hicloud/menukit/model"
	"hexcloud.cn/hicloud/menukit/pkg/log"
	"hexcloud.cn/hicloud/menukit/pkg/utils"
)

func buildPriceVersionByResponse(respBody []byte, currentInfo *model.StorePriceVersionInfoResponse, queryType model.PriceQueryType) (*model.StorePriceVersionInfoResponse, bool, error) {
	// 检查响应是否为空
	hasUpdated := false
	if len(respBody) == 0 {
		return nil, false, errors.New("响应体为空")
	}

	// 使用jsonparser解析payload部分
	payload, _, _, err := jsonparser.Get(respBody, "payload", "priceVersion")
	if err != nil {
		return nil, false, fmt.Errorf("解析payload字段失败: %w", err)
	}
	if len(payload) == 0 {
		return nil, false, errors.New("响应中缺少payload.priceCenter字段")
	}
	log.Logger.Infof("成功获取云端数据: price，大小: %s", utils.FormatDataSize(len(payload)))
	priceVersionResp := &model.StorePriceVersionInfoResponse{}
	err = json.Unmarshal(payload, priceVersionResp)
	if err != nil {
		return nil, false, fmt.Errorf("解析priceCenter失败: %w", err)
	}

	// 获取拉取时间
	if priceVersionResp.PullTime == "" {
		return nil, false, errors.New("响应中缺少pull_time字段")
	}

	// 获取查询类型
	if qt, ok := model.PriceQueryTypeMap[priceVersionResp.QueryType]; ok {
		queryType = qt
	}

	// 初始化结果
	result := currentInfo

	switch queryType {
	// 全量查询 直接覆盖
	case model.PriceQueryTypeAll, model.PriceQueryTypeCurrent:
		result = priceVersionResp
		hasUpdated = true
		// 只查询modify 则合并
	case model.PriceQueryTypeModify:
		// 查询修改价格，提取 modify 部分
		if len(priceVersionResp.Rows) == 0 {
			log.Logger.Infof("priceVersion:本地数据和云端数据一致，使用本地数据")
		}
		if len(priceVersionResp.Rows) != 0 {
			result, err = mergeIncrPriceVersionIntoFile(result, priceVersionResp)
			if err != nil {
				return nil, hasUpdated, fmt.Errorf("合并修改信息失败: %w", err)
			}
			// 增量查询时 需要检查modify 元素是否已生效，如果已生效则更新rows，并删除modify中的元素
			hasUpdated = true
		}

	default:
		return nil, hasUpdated, errors.New("不支持的查询类型")
	}
	// 设置批次ID和拉取时间
	result.BatchId = priceVersionResp.BatchId
	result.PullTime = priceVersionResp.PullTime
	result.QueryType = priceVersionResp.QueryType

	return result, hasUpdated, nil
}

func mergeIncrPriceVersionIntoFile(currentInfo *model.StorePriceVersionInfoResponse, incrInfo *model.StorePriceVersionInfoResponse) (*model.StorePriceVersionInfoResponse, error) {
	currentInfo.Rows = append(currentInfo.Rows, incrInfo.Rows...)
	// 根据batchID 排序
	sort.Slice(currentInfo.Rows, func(i, j int) bool {
		return cast.ToUint64(currentInfo.Rows[i].PriceVersion.BatchId) < cast.ToUint64(currentInfo.Rows[j].PriceVersion.BatchId)
	})
	return currentInfo, nil
}

// 获取价格文件的中的version_batch_id
// 获取价格版本信息中的batch_id
//
//	batch_id 小于 batch_id 的部分为已合并的数据
//	batch_id 大于 batch_id 的部分为待合并的数据
func GetPriceVersionFromFile(ctx context.Context, getFunc func(string) ([]byte, error), saveFunc func([]byte, string) error) (*model.StorePriceVersionInfoResponse, error) {
	// 获取价格文件的中的version_batch_id
	return nil, nil
}
