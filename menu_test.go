package menukit

import (
	"context"
	"encoding/json"
	"os"
	"testing"
	"time"

	"github.com/buger/jsonparser"
	"hexcloud.cn/hicloud/menukit/model"
	log "hexcloud.cn/hicloud/menukit/pkg/log"
	"hexcloud.cn/hicloud/menukit/pkg/utils"
	"hexcloud.cn/hicloud/menukit/service/price"
)

func TestGetMenuData(t *testing.T) {
	token := "4wdjnZVivVoCVS_B0wUgMfsuPPr8CiYzCOSxeqQiRUw.pYDl9EM8Jl9kEnHmRGC4g36Tjjdcy6N8lLdrwI8xYoE"
	//storeId := uint64(4908316407994810368)
	storeId := "4877537183948374016"
	host := "https://hipos-saas-qa.hexcloud.cn"
	//host := "http://127.0.0.1:8081"
	//host := "https://saas.idimsum.com"

	dir := "/Users/<USER>/go/src/codeup.aliyun.com/619e3e4fcb55679b040c45b4/hicloud/omnichannel-product-list/json"
	//dir := "/Users/<USER>/go_project/src/codeup.aliyun.com/omnichannel-product-list"
	withSaveFunc := WithSaveFunc(price.DefaultSaveFunc(dir))
	withGetFunc := WithGetFunc(price.DefaultGetFunc(dir))
	err := os.RemoveAll(dir)
	if err != nil {
		return
	}
	log.Logger.Infof("模拟首次抓取数据===============================================")
	start := time.Now()
	menuData, err := GetMenuData(context.Background(), WithHost(host), WithToken(token), WithChannelCode("POS"), WithStoreID(storeId), WithLang("zh-CN"), WithPartnerID("1372"), withSaveFunc, withGetFunc, WithMaxRetries(0))
	elapsed1 := time.Since(start)
	log.Logger.Infof("首次抓取数据执行时间: %s", elapsed1)
	bs, _ := json.Marshal(menuData)
	log.Logger.Infof("菜单数据获取完成================  数据大小: %s", utils.FormatDataSize(len(bs)))
	if err != nil {
		t.Errorf("PullBaseData error: %v", err)
	}

	log.Logger.Infof("模拟增量抓取数据===============================================")
	start = time.Now()
	menuData, err = GetMenuData(context.Background(), WithHost(host), WithToken(token), WithChannelCode("POS"), WithStoreID(storeId), WithLang("zh-CN"), WithPartnerID("1372"), withSaveFunc, withGetFunc, WithMaxRetries(0))
	elapsed2 := time.Since(start)
	log.Logger.Infof("增量获取菜单数据执行时间: %s", elapsed2)

	log.Logger.Infof("时间差异: %s", elapsed1-elapsed2)

	if err := os.WriteFile("menu.json", bs, 0666); err != nil {
		log.Logger.Fatal(err)
	}
}

func TestGetMenuDataSingle(t *testing.T) {
	token := "WCfz3FoChoEMmnf53Si1vuyRcBYkNkNhmd763AiAXSU.ilWNraVkauNV76V8XA_cD6iRe6Te-ImPzwz38_6NyUo"
	//storeId := uint64(4908316407994810368)
	storeId := "4877537183948374016"
	host := "https://hipos-saas-qa.hexcloud.cn"

	//host := "https://saas.idimsum.com"

	dir := "./json"
	//dir := "/Users/<USER>/go_project/src/codeup.aliyun.com/omnichannel-product-list"
	withSaveFunc := WithSaveFunc(price.DefaultSaveFunc(dir))
	withGetFunc := WithGetFunc(price.DefaultGetFunc(dir))
	start := time.Now()
	menuData, err := GetMenuData(context.Background(), WithHost(host), WithToken(token), WithChannelCode("POS"), WithStoreID(storeId), WithLang("zh-CN"), WithPartnerID("1372"), withSaveFunc, withGetFunc, WithMaxRetries(0))
	elapsed1 := time.Since(start)
	log.Logger.Infof("抓取数据执行时间: %s", elapsed1)
	bs, _ := json.Marshal(menuData)
	log.Logger.Infof("菜单数据获取完成================  数据大小: %s", utils.FormatDataSize(len(bs)))
	if err != nil {
		t.Errorf("PullBaseData error: %v", err)
	}

	if err := os.WriteFile("menu.json", bs, 0666); err != nil {
		log.Logger.Fatal(err)
	}
}

func BenchmarkGetMenuData(b *testing.B) {
	token := "z-qVy9SkeQXZjZm2N3_NqT6tzoKvopZbbuSlaOx-3mI.ugo96zHlFF78eIf4nDtlm5GrUV4CwEDba2Ij815nGX8"
	storeId := "4972806615293067264"
	host := "https"
	dir := "/Users/<USER>/go/src/codeup.aliyun.com/619e3e4fcb55679b040c45b4/hicloud/omnichannel-product-list"
	withSaveFunc := WithSaveFunc(price.DefaultSaveFunc(dir))
	withGetFunc := WithGetFunc(price.DefaultGetFunc(dir))

	for i := 0; i < b.N; i++ {
		_, err := GetMenuData(context.Background(), WithHost(host), WithToken(token), WithChannelCode("POS"), WithStoreID(storeId), WithLang("zh-CN"), WithPartnerID("1372"), withSaveFunc, withGetFunc, WithMaxRetries(0))

		if err != nil {
			b.Errorf("GetMenuData error: %v", err)
		}

		//if err := os.WriteFile(fmt.Sprintf("menu_%d.json", i), menuData, 0666); err != nil {
		//	b.Fatalf("write file error: %v", err)
		//}
	}
}

func TestGetMenuDataMarshal(t *testing.T) {
	f := price.DefaultGetFunc("./json")
	bs, _ := f(model.DefaultProductListFileName)
	bs, _, _, _ = jsonparser.Get(bs, "rows")
	list := make([]*model.Category, 0)
	err := json.Unmarshal(bs, &list)
	if err != nil {
		t.Errorf("json unmarshal error: %v", err)
	}
}
