package model

type StorePriceVersionInfoResponse struct {
	Rows    []*StorePriceVersionInfo `json:"rows,omitempty"`
	BatchId string                   `json:"batch_id,omitempty"`
	// pull_time
	PullTime  string `json:"pull_time,omitempty"`
	QueryType string `json:"query_type,omitempty"`
	Total     int64  `json:"total,omitempty"`
}

type StorePriceVersionInfo struct {
	PriceVersion *PriceVersion              `json:"price_version,omitempty"`
	Groups       []*PriceGroupVersionDetail `json:"groups,omitempty"`
}

type PriceVersion struct {
	Id              string   `json:"id,omitempty"`
	Code            string   `json:"code,omitempty"`
	Name            string   `json:"name,omitempty"`
	Description     string   `json:"description,omitempty"`
	EffectiveTime   string   `json:"effective_time,omitempty"`
	EffectiveStatus string   `json:"effective_status,omitempty"`
	GroupIds        []string `json:"group_ids,omitempty"`
	Status          string   `json:"status,omitempty"`
	CreatedName     string   `json:"created_name,omitempty"`
	Created         string   `json:"created,omitempty"`
	UpdatedName     string   `json:"updated_name,omitempty"`
	Updated         string   `json:"updated,omitempty"`
	BatchId         string   `json:"batch_id,omitempty"`
	ProductCount    string   `json:"product_count,omitempty"`
}

type PriceGroupVersionDetail struct {
	Group    *PriceGroupVersion `json:"group,omitempty"`
	Products []*VersionPrice    `json:"products,omitempty"` //   repeated uint64  del_item_ids = 3;
}

type PriceGroupVersion struct {
	// 数据id
	Id string `json:"id,omitempty"`
	// 价格组名称
	// @gotags: validate:"required"
	Name string `json:"name,omitempty" validate:"required"`
	// @gotags: validate:"required"
	// 价格组编号
	Code string `json:"code,omitempty" validate:"required"`
	// 区域id
	// @gotags: validate:"required_without=StoreIds"
	RegionIds []string `json:"region_ids,omitempty" validate:"required_without=StoreIds"`
	// 门店
	// @gotags: validate:"required_without=RegionIds"
	StoreIds []string `json:"store_ids,omitempty" validate:"required_without=RegionIds"`
	// 排除门店
	ExcStoreIds []string `json:"exc_store_ids,omitempty"`
	// 包含商品数量
	ProductCount string `json:"product_count,omitempty"`
	// 价格组状态
	Status string `json:"status,omitempty"`
	// 更新人名称
	UpdatedName string `json:"updated_name,omitempty"`
	// 更新时间
	Updated string `json:"updated,omitempty"`
	// 地理区域名称(计划查询时返回)
	RegionNames []string `json:"region_names,omitempty"`
	// 门店名称（计划查询时返回）
	StoreNames []string `json:"store_names,omitempty"`
	// 排除门店名称（计划查询时返回）
	ExcStoreNames []string `json:"exc_store_names,omitempty"`
	// 排除地理区域id
	ExcRegionIds []string `json:"exc_region_ids,omitempty"`
	// 排除地理区域名称
	ExcRegionNames []string `json:"exc_region_names,omitempty"`
	// 标签id
	// @gotags: validate:"required"
	TagId string `json:"tag_id,omitempty" validate:"required"`
	//标签名称
	TagName string `json:"tag_name,omitempty"`
	// 区域类型
	// @gotags: validate:"required_with=RegionIds"
	RegionType string `json:"region_type,omitempty" validate:"required_with=RegionIds"`
	// 价格类型id
	PriceTypeId string `json:"price_type_id,omitempty"`
	// 币种
	// @gotags: validate:"required"
	Currency string `json:"currency,omitempty" validate:"required"`
	// 生效时间
	EffectiveTime string `json:"effective_time,omitempty"`
	// 生效状态
	EffectiveStatus string `json:"effective_status,omitempty"`
	// 原价格组
	SourceId string `json:"source_id,omitempty"`
	// 原价格组
	SourceName string `json:"source_name,omitempty"`
	// 原价格组
	SourceCode string `json:"source_code,omitempty"`
	// 版本ID
	VersionId string `json:"version_id,omitempty"`
}

type VersionPrice struct {
	// 商品id
	// @gotags: validate:"required"
	ItemId string `json:"item_id,omitempty" validate:"required"`
	// 商品编码
	ItemCode string `json:"item_code,omitempty"`
	// 商品名称
	ItemName string `json:"item_name,omitempty"`
	// 商品规格
	Spec string `json:"spec,omitempty"`
	// 商品订货单位id
	OrderUnitId uint64 `json:"order_unit_id,omitempty"`
	// 商品订货单位名称
	OrderUnitName string `json:"order_unit_name,omitempty"`
	// 销项税率
	TaxRate string `json:"tax_rate,omitempty"`
	// 采购价格(预留)
	PurchasePrice string `json:"purchase_price,omitempty"`
	// 物流模式(预留)
	SendType string `json:"send_type,omitempty"`
	// 规则
	Rule string `json:"rule,omitempty"`
	// 价格类型ID
	// @gotags: validate:"required"
	PriceTypeId   string `json:"price_type_id,omitempty" validate:"required"`
	PriceTypeName string `json:"price_type_name,omitempty"`
	//  价格
	// @gotags: validate:"required"
	Price        string `json:"price,omitempty" validate:"required"`
	PriceGroupId uint64 `json:"price_group_id,omitempty"`
	// 数据ID
	Id uint64 `json:"id,omitempty"`
	// 原价格（TZ task 明细查询专用）
	OldPrice     string `json:"old_price,omitempty"`
	UseDate      string `json:"use_date,omitempty"`
	Hidden       bool   `json:"hidden,omitempty"`
	TakeoutPrice string `json:"takeout_price,omitempty"`
	Status       string `json:"status,omitempty"`
}
