package model

type ProductListRequest struct {
	StoreId string `json:"storeId"`
	Channel string `json:"channel"`
}

type ProductListResponse struct {
	StatusCode int               `json:"status_code"`
	Payload    *Payload          `json:"payload"`
	Success    bool              `json:"success"`
	Message    string            `json:"message"`
	BaseData   *BaseDataResponse `json:"base_data"`
}

type Payload struct {
	MenuId     string      `json:"menuId"`
	Name       string      `json:"name"`
	Category   []*Category `json:"categoryList"`
	UpdateTime string      `json:"updateTime"`
	Version    int         `json:"version"`
}

type Category struct {
	Created           interface{} `json:"created"`
	CreatedBy         string      `json:"created_by"`
	Updated           interface{} `json:"updated"`
	UpdatedBy         string      `json:"updated_by"`
	MenuCategoryId    string      `json:"menuCategoryId"`
	CategoryParentId  string      `json:"categoryParentId"`
	CategoryId        string      `json:"categoryId" storm:"id"`
	CategoryName      string      `json:"categoryName"`
	Sort              string      `json:"sort"`
	Picture           string      `json:"picture"`
	ProductList       []*Product  `json:"productList"`
	CategoryNameLan   []*LangList `json:"categoryNameLan"`
	ChannelSupportTag []string    `json:"channelSupportTag"`
}

type LangList struct {
	Lan   string `json:"lan"`
	Value string `json:"value"`
}

type Product struct {
	Created                    string      `json:"created"`
	CreatedBy                  string      `json:"created_by"`
	Updated                    string      `json:"updated"`
	UpdatedBy                  string      `json:"updated_by"`
	Name                       string      `json:"name"`
	NameLan                    []*LangList `json:"nameLan"`
	MnemonicCode               []string    `json:"mnemonic_code"`
	DealId                     []string    `json:"deal_id"`
	FirstPinyin                []string    `json:"first_pinyin"`
	ProductCategory            []string    `json:"product_category"`
	ProductId                  string      `json:"product_id"`
	Upc                        []string    `json:"upc"`
	SellingPrice               string      `json:"selling_price"`
	OriginalPrice              string      `json:"original_price"`
	Code                       string      `json:"code"`
	PriceType                  string      `json:"price_type"`
	SaleType                   string      `json:"sale_type"`
	Unit                       string      `json:"unit"`
	IsJoinQueue                bool        `json:"is_join_queue,omitempty"`
	Relation                   *Relation   `json:"relation"`
	ChannelAvailablePeriod     string      `json:"channel_available_period"`
	ChannelAvailablePeriodType string      `json:"channel_available_period_type"`
	ChannelDescription         string      `json:"channel_description"`
	ChannelDescriptionLan      []*LangList `json:"channel_description_lan"`
	AppChannelDescription      string      `json:"app_channel_description"`
	ChannelRemark              string      `json:"channel_remark"`
	Picture                    string      `json:"picture"`
	ChannelSimpleImage         interface{} `json:"channel_simple_image"`
	ChannelPicture             interface{} `json:"channel_picture"`
	PictureDisplaySize         string      `json:"pictureDisplaySize"`
	SetType                    string      `json:"set_type"`
	Status                     string      `json:"status"`
	ChannelTag                 interface{} `json:"channel_tag"`
	ChannelPackageFixedFee     float64     `json:"channel_package_fixed_fee"`
	ChannelHasCupLabelNotice   bool        `json:"channel_has_cup_label_notice"`
	ChannelCupLabelNotice      string      `json:"channel_cup_label_notice"`
	MakeTime                   string      `json:"make_time"`
	ChannelNoCupCounted        bool        `json:"channel_no_cup_counted"`
	ChannelHasCupCounted       bool        `json:"channelHasCupCounted"`
	SellSpecification          *struct {
		Data []SellSpecification `json:"data"`
	} `json:"sell_specification"`
	IsAdditionalAttribute           bool                `json:"is_additional_attribute"`
	IsAddition                      bool                `json:"is_addition"`
	ProductShelfStatus              bool                `json:"productShelfStatus"`
	WeightProduct                   bool                `json:"weightProduct"`
	Stock                           interface{}         `json:"stock"`
	StockOperate                    bool                `json:"stockOperate"`
	ProductPrintName                string              `json:"product_print_name"`
	ProductPrintNameLan             []*LangList         `json:"product_print_name_lan"`
	ShowColor                       string              `json:"show_color"`
	Sort                            int                 `json:"sort"`
	ServingSize                     string              `json:"serving_size,omitempty"`
	IsCupNotice                     bool                `json:"is_cup_notice"`
	ChannelMinBuyCount              int                 `json:"channelMinBuyCount,omitempty"`
	SplitCharging                   bool                `json:"split_charging"`
	MergeTopping                    bool                `json:"merge_topping"`
	DifferentChannelAvailablePeriod interface{}         `json:"different_channel_available_period"`
	ChannelSupportTag               []string            `json:"channelSupportTag"`
	BowlCount                       int                 `json:"bowl_count"`
	RadioBowOptions                 string              `json:"radio_bow_options"`
	MenuCategoryId                  string              `json:"menuCategoryId"`
	MenuCategoryName                string              `json:"menuCategoryName"`
	MenuCategoryPicture             string              `json:"menuCategoryPicture"`
	ChannelItemPriceMap             map[string]float64  `json:"channelItemPriceMap"`
	ItemGroupList                   []*ProductItemGroup `json:"itemGroupList"`
	ItemGroupPosList                []*ProductItemGroup `json:"itemGroupPosList"`
	AutoLinkedDiningMethodProducts  interface{}         `json:"autoLinkedDiningMethodProducts"`
	RecommendGroupList              []*RecommendGroup   `json:"recommendGroupList"`
	BasicSetSubitemId               string              `json:"basicSetSubitemId"`
	ExtCode                         string              `json:"extCode,omitempty"`
	ShowType                        string              `json:"showType"`
	FeedLabel                       string              `json:"feedLabel"`
	Currency                        string              `json:"currency"`
	ChannelLabel                    []string            `json:"channelLabel"`
	AdultStatus                     bool                `json:"adultStatus"`
	AdultNotice                     string              `json:"adultNotice"`
	RecommendStatus                 *bool               `json:"recommendStatus"`
	ShowPriceStatus                 *bool               `json:"showPriceStatus"`
	PriceUp                         *bool               `json:"priceUp"`
	InvisibleStatus                 *bool               `json:"invisibleStatus"`
	DisplayMode                     string              `json:"displayMode"`
	Periods                         []string            `json:"periods"`
	SaleStatusByPeriod              *bool               `json:"saleStatusByPeriod"`
	ProductBusinessType             string              `json:"productBusinessType"`
	ProductChannelProperties        interface{}         `json:"productChannelProperties"`
}

type SellSpecification struct {
	Id          string                    `json:"id"`
	Code        string                    `json:"code"`
	AttrName    string                    `json:"attrName"`
	AttrNameLan []*LangList               `json:"attrNameLan"`
	Remark      string                    `json:"remark"`
	Values      []*SellSpecificationValue `json:"values"`
}

type SellSpecificationValue struct {
	Code      string      `json:"code"`
	Label     string      `json:"label"`
	LabelLang []*LangList `json:"labelLan"`
	Value     string      `json:"value"`
	Checked   bool        `json:"checked"`
	Id        string      `json:"id"`
}

type Sku struct {
	Id string `json:"id"`
	//ProductId       string      `json:"product_id"`
	ShelfStatus         bool               `json:"shelfStatus"`
	Code                string             `json:"code"`
	SellingPrice        float64            `json:"selling_price"`
	OriginalPrice       float64            `json:"original_price"`
	Currency            string             `json:"currency"`
	Status              interface{}        `json:"status"`
	Upc                 []string           `json:"upc"`
	AttrSingleIndex     string             `json:"attr_single_index"`
	AttrNames           interface{}        `json:"attr_names"`
	AttrValues          interface{}        `json:"attr_values"`
	Stock               int                `json:"stock"`
	StockOperate        bool               `json:"stockOperate"`
	SkuMutexAttr        []interface{}      `json:"sku_mutex_attr"`
	SaleName            string             `json:"sale_name"`
	SaleNameLan         []*LangList        `json:"sale_name_lan"`
	ChannelItemPriceMap map[string]float64 `json:"channelItemPriceMap"`
}

type Addition struct {
	Name                 string      `json:"name"`
	NameLan              []*LangList `json:"nameLan"`
	Code                 string      `json:"code"`
	Id                   string      `json:"id"`
	SpuID                string      `json:"spuId"`
	RaisePrice           float64     `json:"raise_price"`
	Currency             string      `json:"currency"`
	ChannelHasCupCounted bool        `json:"channelHasCupCounted"`
	// 一下的数据应该是废弃的
	GroupCode               string                 `json:"groupCode,omitempty"`
	GroupName               string                 `json:"groupName,omitempty"`
	GroupNameLan            []*LangList            `json:"groupNameLan"`
	SplitCharging           bool                   `json:"splitCharging,omitempty"`
	CalculateInParent       bool                   `json:"calculate_in_parent"`
	GroupMaxCount           int                    `json:"groupMaxCount,omitempty"`
	GroupMinCount           int                    `json:"groupMinCount,omitempty"`
	NotRepeatSelectInGroup  bool                   `json:"notRepeatSelectInGroup"`
	FeedLabel               string                 `json:"feedLabel"`
	Picture                 string                 `json:"picture"`
	GroupType               string                 `json:"groupType"`
	SelectedCount           int                    `json:"selectedCount"`
	MutexFeedAttrIds        []string               `json:"mutexFeedAttrIds"`
	ProductPrintName        string                 `json:"product_print_name"`
	AdditionCountMax        interface{}            `json:"additionCountMax"`
	AdditionCountMin        interface{}            `json:"additionCountMin"`
	DefaultOptionalMaxCount interface{}            `json:"default_optional_max_count"`
	DefaultOptionalMinCount interface{}            `json:"default_optional_min_count"`
	FreeAmount              interface{}            `json:"freeAmount"`
	Order                   int                    `json:"order"` //加料顺序
	AdditionalAttribute     []*AdditionalAttribute `json:"additional_attribute"`
	GroupColorConfigId      string                 `json:"groupColorConfigId"`
	AdditionColorConfigId   string                 `json:"additionColorConfigId"`
	DisplayMode             []string               `json:"displayMode"`
	ChannelItemPriceMap     map[string]float64     `json:"channelItemPriceMap"`
	DefaultSelected         bool                   `json:"defaultSelected"`
	GroupFreeQuantity       int                    `json:"groupFreeQuantity"`
	GroupFreePrice          float64                `json:"groupFreePrice"`
	GroupFreeItemPriceMap   map[string]float64     `json:"groupFreeItemPriceMap"`
	ItemGroupId             string                 `json:"itemGroupId"`
	WeightProduct           bool                   `json:"weightProduct"`
	ModifyCode              string                 `json:"modifyCode"`
	GroupModifyCode         string                 `json:"groupModifyCode"`
}

type Set struct {
	Name                     string             `json:"name"`
	NameLan                  []*LangList        `json:"nameLan"`
	Code                     string             `json:"code"`
	Price                    float64            `json:"price"`
	Currency                 string             `json:"currency"`
	KindName                 string             `json:"kind_name"`
	KindNameLan              []*LangList        `json:"kind_name_lan"`
	Mandatory                bool               `json:"mandatory"`
	ProductCount             int                `json:"product_count"`
	CountLimit               int                `json:"count_limit"`
	MinCountLimit            int                `json:"min_count_limit"`
	LabelName                string             `json:"label_name"`
	ProductId                string             `json:"product_id"`
	SubItemSpu               *Product           `json:"subItemSpu"`
	ChannelRemark            string             `json:"channel_remark"`
	ChannelHasCupLabelNotice bool               `json:"channel_has_cup_label_notice"`
	ChannelCupLabelNotice    string             `json:"channel_cup_label_notice"`
	ChannelNoCupCounted      bool               `json:"channel_no_cup_counted"`
	ProductPrintName         string             `json:"product_print_name"`
	RaisePrice               float64            `json:"raisePrice"`
	DefaultFreeFeed          string             `json:"default_free_feed"`
	OptionalFeed             string             `json:"optional_feed"`
	MaxOptionalCount         int                `json:"max_optional_count"`
	MinOptionalCount         int                `json:"min_optional_count"`
	NotRepeatSelectInGroup   bool               `json:"not_repeat_select_in_group"`
	Stock                    int                `json:"stock"`
	OverSelect               bool               `json:"over_select"`
	OverSelectPrice          float64            `json:"over_select_price"`
	ItemColorConfigId        string             `json:"itemColorConfigId"`
	GroupColorConfigId       string             `json:"group_color_config_id"`
	ChannelItemPriceMap      map[string]float64 `json:"channelItemPriceMap"`
	DefaultSelected          bool               `json:"defaultSelectionStatus"`
	TypePrice                string             `json:"priceType"`
	GroupPrice               float64            `json:"groupPrice"`
	ItemGroupId              string             `json:"itemGroupId"`
	Relation                 *Relation          `json:"relation"`
	SpuId                    string             `json:"spu_id"`
	Addition                 []*Addition        `json:"addition"`
	ModifyCode               string             `json:"modifyCode"`
	TmpRaisePrice            float64            `json:"tmpRaisePrice"`
}

type AttributeValues struct {
	Code                            string         `json:"code"`                   //编码
	DefaultSelectionStatus          bool           `json:"defaultSelectionStatus"` //是否默认选择
	EnableStatus                    bool           `json:"enableStatus"`           //开启状态
	ExtCode                         string         `json:"extCode"`                //外卖编码(字母加数字4位字符不可重复)
	Id                              string         `json:"id"`
	Name                            string         `json:"name"` //  属性值
	NameLan                         []*LangList    `json:"nameLan"`
	Price                           float64        `json:"price"`     //属性价格
	PrintName                       string         `json:"printName"` //打印值名称
	PrintNameLan                    []*LangList    `json:"printNameLan"`
	Order                           int            `json:"order"`         //pos属性值顺序
	PrintOrder                      int            `json:"printOrder"`    //打印显示值顺序
	ReadyStatus                     bool           `json:"readyStatus"`   //是否是现制品
	SaleStatus                      bool           `json:"saleStatus"`    //
	SelfHelpOrder                   int            `json:"selfHelpOrder"` //自取显示值顺序
	ShowName                        string         `json:"showName"`      //显示值名称
	ShowNameLan                     []*LangList    `json:"showNameLan"`   //显示值名称
	ColorConfigId                   string         `json:"colorConfigId"`
	DisplayMode                     []string       `json:"displayMode"`
	DifferentChannelAvailablePeriod *BusinessHours `json:"differentChannelAvailablePeriod"` //属性售卖时段
	ExemptsSelectionAttr            []string       `json:"exemptsSelectionAttr"`            //排除必选属性

	TakeoutOrder        int                `json:"takeoutOrder"` //外卖显示值顺序
	Stock               int                `json:"stock"`
	StockOperate        bool               `json:"stockOperate"`
	ShelfStatus         bool               `json:"shelfStatus"` //上下架状态
	MutexAttrIds        []string           `json:"mutexAttrIds"`
	MutexSkuAttrIds     []string           `json:"mutexSkuAttrIds"`
	MutexFeedAttrIds    []string           `json:"mutexFeedAttrIds"`
	CalculateInParent   bool               `json:"calculate_in_parent"` //价格是否计算到display amount
	ChannelItemPriceMap map[string]float64 `json:"channelItemPriceMap"`
	SecondLevelAttr     []string           `json:"secondLevelAttr"` //属性值的子集属性组ids
	EffectChoose        bool               `json:"effectChoose"`    //当前属性值是否可选 有子级属性的情况
	MinOptionalQuantity int                `json:"minOptionalQuantity"`
	MaxOptionalQuantity int                `json:"maxOptionalQuantity"`
}

type TimeSlot struct {
	BeginTime string `json:"beginTime"`
	EndTime   string `json:"endTime"`
}

type BusinessDay struct {
	Day  string      `json:"day"`
	Time []*TimeSlot `json:"time"`
}

type BusinessHours struct {
	BusinessDays []*BusinessDay `json:"businessDays"`
	ChoosedType  string         `json:"choosedType"`
}
type AttributeLabels struct {
	Id         string `json:"id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Type       string `json:"type"`
	Color      string `json:"color"`
	BgColor    string `json:"bgColor"`
	StartTime  string `json:"startTime"`
	EndTime    string `json:"endTime"`
	OpenStatus string `json:"openStatus"`
}

type AdditionalAttribute struct {
	ShelfStatus         bool               `json:"shelfStatus"`        //上下架状态
	AttributeName       string             `json:"attribute_name"`     //  附加属性名称
	AttributeNameLan    []LangList         `json:"attribute_name_lan"` //  附加属性名称
	Code                string             `json:"code"`               //属性CODE
	Id                  string             `json:"id"`                 //  属性ID
	PrintName           string             `json:"printName"`          //  打印名称
	PrintNameLan        []LangList         `json:"print_name_lan"`     //  附加属性名称
	Order               int                `json:"order"`              //排序
	SelectedMaxCount    int                `json:"selectedMaxCount"`   //选择上限
	SelectedStatus      bool               `json:"selectedStatus"`     //是否必须
	SingleOrMult        bool               `json:"singleOrMult"`       //单选或者多选
	PrintOrder          int                `json:"printOrder"`         //属性组排序
	AttributeValues     []*AttributeValues `json:"attribute_values"`
	AttributeCondition  []interface{}      `json:"attribute_condition"`
	AttributeLabels     []*AttributeLabels `json:"attributeLabels"`
	Remark              string             `json:"remark"`
	ColorConfigId       string             `json:"colorConfigId"`
	PrintGroupValue     string             `json:"printGroupValue"` //设置为值一样的分组会打印同一行显示
	FreeSetup           bool               `json:"freeSetup"`       //当前属性组是否免费
	ItemGroupId         string             `json:"itemGroupId"`
	MinOptionalQuantity int                `json:"minOptionalQuantity"`
	MaxOptionalQuantity int                `json:"maxOptionalQuantity"`
	StockOperation      bool               `json:"stockOperation"`
}

type Relation struct {
	ProductLabel        []*ProductLabel        `json:"productLabel,omitempty"`
	ProductCategory     string                 `json:"product_category"`
	Sku                 []*Sku                 `json:"sku"`
	Addition            []*Addition            `json:"addition"`
	Set                 []*Set                 `json:"set"`
	Unit                string                 `json:"unit"`
	AdditionalAttribute []*AdditionalAttribute `json:"additional_attribute"`
	SecondLevelAttrList []*AdditionalAttribute `json:"secondLevelAttrList"`
}

type ProductLabel struct {
	Id         string `json:"id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Color      string `json:"color"`
	BgColor    string `json:"bgColor"`
	OpenStatus string `json:"openStatus"`
}

type RecommendGroup struct {
	ItemGroupId      string           `json:"group_item_group_id"` // 对应 @JSONField(name = "group_item_group_id")
	ItemGroupName    string           `json:"itemGroupName"`
	ItemGroupNameLan []*LangList      `json:"itemGroupNameLan"`
	GroupOrder       int              `json:"group_order"` // 对应 @JSONField(name = "group_order")
	MinCount         int              `json:"min_count"`   // 对应 @JSONField(name = "min_count")
	MaxCount         int              `json:"max_count"`   // 对应 @JSONField(name = "max_count")
	Items            []*RecommendItem `json:"items"`
}

type RecommendItem struct {
	ID                         string             `json:"id"`
	Code                       string             `json:"code"`
	Name                       string             `json:"name"`
	Description                string             `json:"description"`
	ItemGroupId                string             `json:"item_group_id"`
	ItemGroupName              string             `json:"itemGroupName"`
	ItemGroupNameLan           []*LangList        `json:"itemGroupNameLan"`
	ProductMinCount            int                `json:"product_min_count"`
	ProductMaxCount            int                `json:"product_max_count"`
	MinCount                   int                `json:"min_count"`
	MaxCount                   int                `json:"max_count"`
	GroupOrder                 int                `json:"group_order"`
	DisplayMode                string             `json:"display_mode"`
	Order                      int                `json:"order"`
	GroupItemDescription       string             `json:"group_item_description"`
	GroupItemDescriptionLanStr string             `json:"group_item_description_lan"`
	GroupItemDescriptionLan    []*LangList        `json:"groupItemDescriptionLan"`
	ModifyCode                 string             `json:"modify_code"`
	Price                      float64            `json:"price"`
	SaleStatusByPeriod         bool               `json:"saleStatusByPeriod"`
	Periods                    []string           `json:"periods"`
	ChannelProductVO           *Product           `json:"channelProductVO"`
	ChannelItemPriceMap        map[string]float64 `json:"channelItemPriceMap"`
}

type ProductItemGroup struct {
	ID             string      `json:"id"`             // 项目组ID
	Code           string      `json:"code"`           // 编码
	Name           string      `json:"name"`           // 名称
	NameLan        []*LangList `json:"nameLan"`        // 名称多语言
	Description    string      `json:"description"`    // 项目组描述
	DescriptionLan []*LangList `json:"descriptionLan"` // 描述多语言

	// 以下字段在 Java 中被注释掉了，根据需要可以取消注释
	// ItemIDs             []string                      `json:"itemIds"`               // 项目IDs（Set -> []string）
	// ItemGroupIDs        []string                      `json:"itemGroupIds"`          // 项目组IDs
	// SpuItemGroupIDMap   map[string][]string           `json:"spuItemGroupIdMap"`     // 商品的项目IDs映射
	// ProductList         []*Product                    `json:"productList"`           // 套餐商品的主商品
	// ProductIDs          []string                      `json:"productIds"`            // 主商品IDs
	// ShowPictureProductID string                       `json:"showPictureProductId"`  // 展示图片用的商品ID
}

type BaseDataResponse struct {
	ProductList   []*Category
	ProductInfo   []*Product
	ProductAttr   []*AdditionalAttribute
	PriceInfo     *PriceInfo
	PriceTypeInfo []*PriceType
	StockList     []map[string]interface{}
}
