package model

import (
	"net/http"
	"time"
)

const (
	// DefaultHost is the default API host
	DefaultHost = "https://hipos-saas-qa.hexcloud.cn"

	// API endpoints
	DefaultChannelProductListApi = "/api/omnichannel/channel/product/list/v2"
	DefaultChannelProductInfoApi = "/api/omnichannel/channel/product/productInfo/list"
	DefaultChannelProductAttrApi = "/api/omnichannel/channel/product/attr/list"
	DefaultPriceApi              = "/api/v1/price-center/get-product-price-full-info-agent"
	DefaultBaseDataAPi           = "/api/menu-center-pos/menu/queryPosMenuProduct"

	DefaultPriceFileName        = "price.json"
	DefaultProductListFileName  = "product_list.json"
	DefaultProductInfoFileName  = "product_info.json"
	DefaultProductAttrFileName  = "product_attr.json"
	DefaultPriceTypeFileName    = "price_type.json"
	DefaultStockListFileName    = "stock_list.json"
	DefaultPriceVersionFileName = "price_version.json"

	// 价格过滤相关
	PriceUnavailable = float64(-9999)
	FLEX_SET         = "FLEX_SET"
)

// GlobalHTTPClient is the global HTTP client for connection reuse
var GlobalHTTPClient = &http.Client{
	Timeout: 30 * time.Second,
}

// PriceQueryType represents the price query type
type PriceQueryType int

const (
	// PriceQueryTypeCurrent queries current price, modify info is not returned
	PriceQueryTypeCurrent PriceQueryType = 0
	// PriceQueryTypeModify queries Modify, only returns outermost Modify
	PriceQueryTypeModify PriceQueryType = 2
	// PriceQueryTypeAll queries all
	PriceQueryTypeAll PriceQueryType = 3
)

var PriceQueryTypeMap = map[string]PriceQueryType{
	"PriceQueryTypeCurrent": PriceQueryTypeCurrent,
	"PriceQueryTypeModify":  PriceQueryTypeModify,
	"PriceQueryTypeAll":     PriceQueryTypeAll,
}
