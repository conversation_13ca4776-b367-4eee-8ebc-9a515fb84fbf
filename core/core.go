// Package core provides core functionality for the menukit package.
package core

import (
	"context"

	"hexcloud.cn/hicloud/menukit"
	"hexcloud.cn/hicloud/menukit/model"
	"hexcloud.cn/hicloud/menukit/service/menu"
	"hexcloud.cn/hicloud/menukit/service/price"
)

// PullBaseData pulls menu base information from API
func PullBaseData(ctx context.Context, host string, token string, params *menukit.GetMenuParams, options ...menukit.Option) (*menu.ProductListOpt, error) {
	return menukit.PullBaseData(ctx, params, options...)
}

// GetMenuDataLegacy gets menu data from file or cloud (legacy function for backward compatibility)
func GetMenuDataLegacy(ctx context.Context, host string, token string, params *menukit.GetMenuParams, options ...menukit.Option) (*model.ProductListResponse, error) {
	return menukit.GetMenuDataLegacy(ctx, params, options...)
}

// GetPriceInfoFromFile gets price information from file
func GetPriceInfoFromFile(queryTime int64, getFunc func(string) ([]byte, error), saveFunc func([]byte, string) error) (*model.PriceInfo, error) {
	return price.GetPriceInfoFromFile(queryTime, getFunc, saveFunc)
}
